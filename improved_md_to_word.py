#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的学术论文 Markdown 到 Word 转换器
专门处理数学公式的转换问题
"""

import re
from docx import Document
from docx.shared import Inches, Pt, Cm
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn


def latex_to_unicode(latex_text):
    """将常见的 LaTeX 符号转换为 Unicode 字符"""

    # 希腊字母映射
    greek_letters = {
        r'\\alpha': 'α', r'\\beta': 'β', r'\\gamma': 'γ', r'\\delta': 'δ',
        r'\\epsilon': 'ε', r'\\zeta': 'ζ', r'\\eta': 'η', r'\\theta': 'θ',
        r'\\iota': 'ι', r'\\kappa': 'κ', r'\\lambda': 'λ', r'\\mu': 'μ',
        r'\\nu': 'ν', r'\\xi': 'ξ', r'\\pi': 'π', r'\\rho': 'ρ',
        r'\\sigma': 'σ', r'\\tau': 'τ', r'\\upsilon': 'υ', r'\\phi': 'φ',
        r'\\chi': 'χ', r'\\psi': 'ψ', r'\\omega': 'ω',
        r'\\Gamma': 'Γ', r'\\Delta': 'Δ', r'\\Theta': 'Θ', r'\\Lambda': 'Λ',
        r'\\Xi': 'Ξ', r'\\Pi': 'Π', r'\\Sigma': 'Σ', r'\\Phi': 'Φ',
        r'\\Psi': 'Ψ', r'\\Omega': 'Ω'
    }

    # 数学符号映射
    math_symbols = {
        r'\\sum': '∑', r'\\prod': '∏', r'\\int': '∫',
        r'\\infty': '∞', r'\\partial': '∂', r'\\nabla': '∇',
        r'\\pm': '±', r'\\mp': '∓', r'\\times': '×', r'\\div': '÷',
        r'\\cdot': '·', r'\\bullet': '•', r'\\circ': '∘',
        r'\\leq': '≤', r'\\geq': '≥', r'\\neq': '≠', r'\\approx': '≈',
        r'\\equiv': '≡', r'\\propto': '∝', r'\\sim': '∼',
        r'\\in': '∈', r'\\notin': '∉', r'\\subset': '⊂', r'\\supset': '⊃',
        r'\\subseteq': '⊆', r'\\supseteq': '⊇', r'\\cup': '∪', r'\\cap': '∩',
        r'\\emptyset': '∅', r'\\forall': '∀', r'\\exists': '∃',
        r'\\rightarrow': '→', r'\\leftarrow': '←', r'\\leftrightarrow': '↔',
        r'\\Rightarrow': '⇒', r'\\Leftarrow': '⇐', r'\\Leftrightarrow': '⇔'
    }

    # 应用替换
    result = latex_text

    # 替换希腊字母
    for latex, unicode_char in greek_letters.items():
        result = re.sub(latex + r'\b', unicode_char, result)

    # 替换数学符号
    for latex, unicode_char in math_symbols.items():
        result = re.sub(latex + r'\b', unicode_char, result)

    return result


def process_latex_commands(text):
    """处理 LaTeX 命令"""

    # 处理 \text{} 命令
    text = re.sub(r'\\text\{([^}]+)\}', r'\1', text)

    # 处理 \mathbf{} 命令（粗体）
    text = re.sub(r'\\mathbf\{([^}]+)\}', r'\1', text)

    # 处理 \mathcal{} 命令（花体）
    text = re.sub(r'\\mathcal\{([^}]+)\}', r'\1', text)

    # 处理 \mathrm{} 命令（正体）
    text = re.sub(r'\\mathrm\{([^}]+)\}', r'\1', text)

    # 处理 \left 和 \right 命令
    text = re.sub(r'\\left\(', '(', text)
    text = re.sub(r'\\right\)', ')', text)
    text = re.sub(r'\\left\[', '[', text)
    text = re.sub(r'\\right\]', ']', text)
    text = re.sub(r'\\left\{', '{', text)
    text = re.sub(r'\\right\}', '}', text)
    text = re.sub(r'\\left\|', '|', text)
    text = re.sub(r'\\right\|', '|', text)

    # 处理 \lfloor 和 \rfloor
    text = re.sub(r'\\lfloor', '⌊', text)
    text = re.sub(r'\\rfloor', '⌋', text)

    # 处理 \lceil 和 \rceil
    text = re.sub(r'\\lceil', '⌈', text)
    text = re.sub(r'\\rceil', '⌉', text)

    return text


def process_fractions(text):
    """处理分数"""
    # 改进的分数处理：\frac{a}{b} -> (a)/(b)
    def replace_frac(match):
        numerator = match.group(1).strip()
        denominator = match.group(2).strip()
        return f"({numerator})/({denominator})"

    # 简单的分数处理，避免无限循环
    text = re.sub(r'\\frac\{([^}]+)\}\{([^}]+)\}', replace_frac, text)

    return text


def process_sqrt(text):
    """处理平方根"""
    # \sqrt{x} -> √(x)
    text = re.sub(r'\\sqrt\{([^}]+)\}', r'√(\1)', text)
    return text


def process_subscripts_superscripts(text):
    """处理上下标"""

    # Unicode 上标字符映射
    superscript_map = {
        '0': '⁰', '1': '¹', '2': '²', '3': '³', '4': '⁴', '5': '⁵',
        '6': '⁶', '7': '⁷', '8': '⁸', '9': '⁹', '+': '⁺', '-': '⁻',
        '=': '⁼', '(': '⁽', ')': '⁾', 'a': 'ᵃ', 'b': 'ᵇ', 'c': 'ᶜ',
        'd': 'ᵈ', 'e': 'ᵉ', 'f': 'ᶠ', 'g': 'ᵍ', 'h': 'ʰ', 'i': 'ⁱ',
        'j': 'ʲ', 'k': 'ᵏ', 'l': 'ˡ', 'm': 'ᵐ', 'n': 'ⁿ', 'o': 'ᵒ',
        'p': 'ᵖ', 'r': 'ʳ', 's': 'ˢ', 't': 'ᵗ', 'u': 'ᵘ', 'v': 'ᵛ',
        'w': 'ʷ', 'x': 'ˣ', 'y': 'ʸ', 'z': 'ᶻ'
    }

    # Unicode 下标字符映射
    subscript_map = {
        '0': '₀', '1': '₁', '2': '₂', '3': '₃', '4': '₄', '5': '₅',
        '6': '₆', '7': '₇', '8': '₈', '9': '₉', '+': '₊', '-': '₋',
        '=': '₌', '(': '₍', ')': '₎', 'a': 'ₐ', 'e': 'ₑ', 'h': 'ₕ',
        'i': 'ᵢ', 'j': 'ⱼ', 'k': 'ₖ', 'l': 'ₗ', 'm': 'ₘ', 'n': 'ₙ',
        'o': 'ₒ', 'p': 'ₚ', 'r': 'ᵣ', 's': 'ₛ', 't': 'ₜ', 'u': 'ᵤ',
        'v': 'ᵥ', 'x': 'ₓ'
    }

    def convert_superscript(match):
        content = match.group(1)
        result = ""
        for char in content:
            if char in superscript_map:
                result += superscript_map[char]
            else:
                result += char
        return result

    def convert_subscript(match):
        content = match.group(1)
        result = ""
        for char in content:
            if char in subscript_map:
                result += subscript_map[char]
            else:
                result += char
        return result

    # 处理上标 ^{...}
    text = re.sub(r'\^\{([^}]+)\}', convert_superscript, text)
    # 处理单字符上标 ^x
    text = re.sub(
        r'\^([a-zA-Z0-9])', lambda m: superscript_map.get(m.group(1), m.group(1)), text)

    # 处理下标 _{...}
    text = re.sub(r'_\{([^}]+)\}', convert_subscript, text)
    # 处理单字符下标 _x
    text = re.sub(
        r'_([a-zA-Z0-9])', lambda m: subscript_map.get(m.group(1), m.group(1)), text)

    return text


def clean_latex_formula(formula_text):
    """清理和转换 LaTeX 公式"""

    # 移除多余的空白
    formula_text = re.sub(r'\s+', ' ', formula_text.strip())

    # 处理各种 LaTeX 命令
    formula_text = process_latex_commands(formula_text)
    formula_text = process_fractions(formula_text)
    formula_text = process_sqrt(formula_text)
    formula_text = latex_to_unicode(formula_text)
    formula_text = process_subscripts_superscripts(formula_text)

    # 处理一些特殊的数学函数
    formula_text = re.sub(r'\\max\b', 'max', formula_text)
    formula_text = re.sub(r'\\min\b', 'min', formula_text)
    formula_text = re.sub(r'\\log\b', 'log', formula_text)
    formula_text = re.sub(r'\\exp\b', 'exp', formula_text)

    # 清理剩余的反斜杠
    formula_text = re.sub(r'\\([a-zA-Z]+)', r'\1', formula_text)

    return formula_text


def setup_academic_styles(doc):
    """设置学术论文样式"""

    # 设置页面边距
    sections = doc.sections
    for section in sections:
        section.top_margin = Cm(2.54)
        section.bottom_margin = Cm(2.54)
        section.left_margin = Cm(3.17)
        section.right_margin = Cm(3.17)

    # 设置正文样式
    normal_style = doc.styles['Normal']
    normal_font = normal_style.font
    normal_font.name = '宋体'
    normal_font.size = Pt(12)

    # 设置段落格式
    normal_paragraph = normal_style.paragraph_format
    normal_paragraph.first_line_indent = Pt(24)  # 首行缩进2字符
    normal_paragraph.line_spacing = 1.5  # 1.5倍行距
    normal_paragraph.space_after = Pt(6)

    # 创建论文标题样式
    try:
        title_style = doc.styles.add_style(
            'PaperTitle', WD_STYLE_TYPE.PARAGRAPH)
        title_font = title_style.font
        title_font.name = '黑体'
        title_font.size = Pt(16)
        title_font.bold = True
        title_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
        title_style.paragraph_format.space_before = Pt(0)
        title_style.paragraph_format.space_after = Pt(18)
    except:
        pass

    # 创建一级标题样式
    try:
        heading1_style = doc.styles.add_style(
            'CustomHeading1', WD_STYLE_TYPE.PARAGRAPH)
        heading1_font = heading1_style.font
        heading1_font.name = '黑体'
        heading1_font.size = Pt(14)
        heading1_font.bold = True
        heading1_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
        heading1_style.paragraph_format.space_before = Pt(18)
        heading1_style.paragraph_format.space_after = Pt(6)
        heading1_style.paragraph_format.first_line_indent = Pt(0)
    except:
        pass

    # 创建二级标题样式
    try:
        heading2_style = doc.styles.add_style(
            'CustomHeading2', WD_STYLE_TYPE.PARAGRAPH)
        heading2_font = heading2_style.font
        heading2_font.name = '黑体'
        heading2_font.size = Pt(12)
        heading2_font.bold = True
        heading2_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
        heading2_style.paragraph_format.space_before = Pt(12)
        heading2_style.paragraph_format.space_after = Pt(6)
        heading2_style.paragraph_format.first_line_indent = Pt(0)
    except:
        pass

    # 创建三级标题样式
    try:
        heading3_style = doc.styles.add_style(
            'CustomHeading3', WD_STYLE_TYPE.PARAGRAPH)
        heading3_font = heading3_style.font
        heading3_font.name = '楷体'
        heading3_font.size = Pt(12)
        heading3_font.bold = False
        heading3_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
        heading3_style.paragraph_format.space_before = Pt(6)
        heading3_style.paragraph_format.space_after = Pt(6)
        heading3_style.paragraph_format.first_line_indent = Pt(0)
    except:
        pass

    # 创建公式样式
    try:
        formula_style = doc.styles.add_style(
            'Formula', WD_STYLE_TYPE.PARAGRAPH)
        formula_font = formula_style.font
        formula_font.name = 'Times New Roman'
        formula_font.size = Pt(11)
        formula_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
        formula_style.paragraph_format.space_before = Pt(6)
        formula_style.paragraph_format.space_after = Pt(6)
        formula_style.paragraph_format.first_line_indent = Pt(0)
    except:
        pass

    # 创建参考文献样式
    try:
        ref_style = doc.styles.add_style('Reference', WD_STYLE_TYPE.PARAGRAPH)
        ref_font = ref_style.font
        ref_font.name = '宋体'
        ref_font.size = Pt(10.5)
        ref_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
        ref_style.paragraph_format.space_after = Pt(3)
        ref_style.paragraph_format.first_line_indent = Pt(0)
        ref_style.paragraph_format.left_indent = Pt(21)  # 悬挂缩进
        ref_style.paragraph_format.hanging_indent = Pt(21)
    except:
        pass


def convert_improved_academic_paper(md_file, word_file):
    """改进的学术论文转换函数"""

    # 创建新文档
    doc = Document()

    # 设置学术论文样式
    setup_academic_styles(doc)

    # 读取 Markdown 文件
    with open(md_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # 按行处理
    lines = content.split('\n')
    i = 0
    is_first_heading = True
    in_references = False

    while i < len(lines):
        line = lines[i].strip()

        if not line:
            i += 1
            continue

        # 检查是否进入参考文献部分
        if '参考文献' in line or 'References' in line:
            in_references = True

        # 处理标题
        if line.startswith('# '):
            # 论文主标题
            title = line[2:].strip()
            p = doc.add_paragraph(title)
            try:
                if is_first_heading:
                    p.style = doc.styles['PaperTitle']
                    is_first_heading = False
                else:
                    p.style = doc.styles['CustomHeading1']
            except:
                p.style = doc.styles['Heading 1']

        elif line.startswith('## '):
            # 二级标题
            title = line[3:].strip()
            p = doc.add_paragraph(title)
            try:
                p.style = doc.styles['CustomHeading2']
            except:
                p.style = doc.styles['Heading 2']

        elif line.startswith('### '):
            # 三级标题
            title = line[4:].strip()
            p = doc.add_paragraph(title)
            try:
                p.style = doc.styles['CustomHeading3']
            except:
                p.style = doc.styles['Heading 3']

        # 处理数学公式块（改进版）
        elif line.startswith('$$'):
            # 收集完整的公式块
            formula_lines = []

            # 处理第一行
            first_line = line[2:].strip()
            if first_line:
                formula_lines.append(first_line)

            # 继续读取直到找到结束的 $$
            i += 1
            while i < len(lines):
                current_line = lines[i].strip()
                if current_line.endswith('$$'):
                    # 找到结束标记
                    end_content = current_line[:-2].strip()
                    if end_content:
                        formula_lines.append(end_content)
                    break
                else:
                    formula_lines.append(current_line)
                i += 1

            # 合并公式内容并清理
            formula_text = ' '.join(formula_lines).strip()
            cleaned_formula = clean_latex_formula(formula_text)

            # 添加公式段落
            p = doc.add_paragraph(cleaned_formula)
            try:
                p.style = doc.styles['Formula']
            except:
                p.alignment = WD_ALIGN_PARAGRAPH.CENTER
                for run in p.runs:
                    run.font.name = 'Times New Roman'
                    run.font.size = Pt(11)

        # 处理普通段落
        else:
            # 参考文献特殊处理
            if in_references and re.match(r'^\d+\.', line):
                p = doc.add_paragraph(line)
                try:
                    p.style = doc.styles['Reference']
                except:
                    p.style = doc.styles['Normal']
                    p.paragraph_format.first_line_indent = Pt(0)
                    p.paragraph_format.left_indent = Pt(21)
                    p.paragraph_format.hanging_indent = Pt(21)

            # 检查是否包含行内公式（改进版）
            elif '$' in line and not line.startswith('$$'):
                p = doc.add_paragraph()

                # 改进的行内公式处理
                parts = re.split(r'(\$[^$]+\$)', line)
                for part in parts:
                    if part.startswith('$') and part.endswith('$') and len(part) > 2:
                        # 这是公式
                        formula = part[1:-1]
                        cleaned_formula = clean_latex_formula(formula)
                        run = p.add_run(cleaned_formula)
                        run.font.name = 'Times New Roman'
                        run.font.size = Pt(11)
                    else:
                        # 这是普通文本
                        if part.strip():
                            run = p.add_run(part)
                            run.font.name = '宋体'
                            run.font.size = Pt(12)

                # 设置段落样式
                if not in_references:
                    p.style = doc.styles['Normal']

            else:
                # 普通段落
                if line:
                    p = doc.add_paragraph(line)
                    if not in_references:
                        p.style = doc.styles['Normal']
                    else:
                        try:
                            p.style = doc.styles['Reference']
                        except:
                            p.style = doc.styles['Normal']

        i += 1

    # 保存文档
    doc.save(word_file)
    print(f"改进的学术论文转换完成！Word 文档已保存为: {word_file}")


if __name__ == "__main__":
    # 转换文件
    md_file = "paper_draft.md"
    word_file = "paper_draft_improved.docx"

    try:
        convert_improved_academic_paper(md_file, word_file)
        print("转换成功！")
        print("\n转换改进说明：")
        print("1. 改进了数学公式的处理，支持复杂的 LaTeX 符号")
        print("2. 将常见的 LaTeX 命令转换为 Unicode 字符")
        print("3. 处理上下标、分数、根号等数学表达式")
        print("4. 改进了多行公式块的解析")
        print("5. 保持了学术论文的标准格式")
        print("\n具体改进：")
        print("- 希腊字母：α, β, γ, δ, ε, σ, ρ, π 等")
        print("- 数学符号：∑, ∏, ∫, ∞, ≤, ≥, ∈, ⊂ 等")
        print("- 上下标：x₁, x², y₃ 等")
        print("- 分数：(a)/(b) 格式")
        print("- 根号：√(x) 格式")
    except Exception as e:
        print(f"转换失败: {e}")
        import traceback
        traceback.print_exc()
