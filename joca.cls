%% joca.cls
%% 《计算机应用》期刊 LaTeX 模板类文件
%% 基于期刊官方格式要求制作
%% 作者：AI Assistant
%% 版本：1.0
%% 日期：2024

\NeedsTeXFormat{LaTeX2e}
\ProvidesClass{joca}[2024/01/01 v1.0 Journal of Computer Applications LaTeX Template]

% 基于 article 类
\LoadClass[a4paper,10.5pt]{article}

% 必需的宏包
\RequirePackage[UTF8,scheme=plain]{ctex}
\RequirePackage{geometry}
\RequirePackage{fancyhdr}
\RequirePackage{titlesec}
\RequirePackage{titletoc}
\RequirePackage{graphicx}
\RequirePackage{amsmath,amsfonts,amssymb}
\RequirePackage{array}
\RequirePackage{booktabs}
\RequirePackage{longtable}
\RequirePackage{multirow}
\RequirePackage{multicol}
\RequirePackage{xcolor}
\RequirePackage{url}
\RequirePackage{hyperref}
\RequirePackage{cite}
\RequirePackage{enumitem}
\RequirePackage{caption}
\RequirePackage{subcaption}
\RequirePackage{float}

% 页面设置
\geometry{
    a4paper,
    top=2.5cm,
    bottom=2.5cm,
    left=2.5cm,
    right=2.5cm
}

% 字体设置
\setCJKmainfont{SimSun}[BoldFont=SimHei]
\setCJKsansfont{SimHei}
\setCJKmonofont{FangSong}

% 行距设置
\linespread{1.2}

% 段落设置
\setlength{\parindent}{2em}
\setlength{\parskip}{0pt}

% 标题格式设置
\titleformat{\section}
    {\heiti\fontsize{12pt}{14.4pt}\selectfont\bfseries}
    {\thesection}
    {1em}
    {}

\titleformat{\subsection}
    {\heiti\fontsize{10.5pt}{12.6pt}\selectfont\bfseries}
    {\thesubsection}
    {1em}
    {}

\titleformat{\subsubsection}
    {\songti\fontsize{10.5pt}{12.6pt}\selectfont\bfseries}
    {\thesubsubsection}
    {1em}
    {}

% 标题间距设置
\titlespacing{\section}{0pt}{12pt}{6pt}
\titlespacing{\subsection}{0pt}{9pt}{6pt}
\titlespacing{\subsubsection}{0pt}{6pt}{3pt}

% 论文标题命令
\newcommand{\papertitle}[1]{
    \begin{center}
        {\heiti\fontsize{14pt}{16.8pt}\selectfont\bfseries #1}
    \end{center}
    \vspace{12pt}
}

% 作者信息命令
\newcommand{\paperauthor}[1]{
    \begin{center}
        {\songti\fontsize{10.5pt}{12.6pt}\selectfont #1}
    \end{center}
    \vspace{6pt}
}

% 作者单位命令
\newcommand{\paperaffiliation}[1]{
    \begin{center}
        {\songti\fontsize{9pt}{10.8pt}\selectfont #1}
    \end{center}
    \vspace{12pt}
}

% 摘要环境
\newenvironment{paperabstract}[1][摘要]{
    \noindent{\heiti\fontsize{10.5pt}{12.6pt}\selectfont\bfseries #1：}
    \fontsize{9pt}{10.8pt}\selectfont
    \setlength{\parindent}{0em}
}{
    \par\vspace{6pt}
}

% 关键词命令
\newcommand{\paperkeywords}[2][关键词]{
    \noindent{\heiti\fontsize{10.5pt}{12.6pt}\selectfont\bfseries #1：}
    {\songti\fontsize{9pt}{10.8pt}\selectfont #2}
    \par\vspace{12pt}
}

% 英文摘要环境
\newenvironment{englishabstract}[1][Abstract]{
    \noindent{\bfseries\fontsize{10.5pt}{12.6pt}\selectfont #1: }
    \fontsize{9pt}{10.8pt}\selectfont
    \setlength{\parindent}{0em}
}{
    \par\vspace{6pt}
}

% 英文关键词命令
\newcommand{\englishkeywords}[2][Keywords]{
    \noindent{\bfseries\fontsize{10.5pt}{12.6pt}\selectfont #1: }
    {\fontsize{9pt}{10.8pt}\selectfont #2}
    \par\vspace{12pt}
}

% 图表标题格式
\captionsetup{
    font={small,sf},
    labelfont=bf,
    textfont=normal,
    justification=centering,
    singlelinecheck=false
}

% 图标题在下方
\captionsetup[figure]{
    position=bottom,
    aboveskip=6pt,
    belowskip=6pt
}

% 表标题在上方
\captionsetup[table]{
    position=top,
    aboveskip=6pt,
    beloweskip=6pt
}

% 公式编号格式
\renewcommand{\theequation}{\arabic{equation}}

% 参考文献格式
\renewcommand{\refname}{参考文献}
\renewenvironment{thebibliography}[1]{
    \section*{\refname}
    \fontsize{9pt}{10.8pt}\selectfont
    \list{\@biblabel{\@arabic\c@enumiv}}%
         {\settowidth\labelwidth{\@biblabel{#1}}%
          \leftmargin\labelwidth
          \advance\leftmargin\labelsep
          \@openbib@code
          \usecounter{enumiv}%
          \let\p@enumiv\@empty
          \renewcommand\theenumiv{\@arabic\c@enumiv}}%
    \sloppy
    \clubpenalty4000
    \@clubpenalty \clubpenalty
    \widowpenalty4000%
    \sfcode`\.\@m
}{
    \def\@noitemerr
       {\@latex@warning{Empty `thebibliography' environment}}%
    \endlist
}

% 页眉页脚设置
\pagestyle{fancy}
\fancyhf{}
\fancyhead[C]{\songti\fontsize{9pt}{10.8pt}\selectfont 计算机应用}
\fancyfoot[C]{\songti\fontsize{9pt}{10.8pt}\selectfont \thepage}
\renewcommand{\headrulewidth}{0.4pt}
\renewcommand{\footrulewidth}{0pt}

% 首页页眉页脚
\fancypagestyle{firstpage}{
    \fancyhf{}
    \fancyfoot[C]{\songti\fontsize{9pt}{10.8pt}\selectfont \thepage}
    \renewcommand{\headrulewidth}{0pt}
}

% 超链接设置
\hypersetup{
    colorlinks=true,
    linkcolor=black,
    citecolor=black,
    urlcolor=blue,
    bookmarksnumbered=true,
    bookmarksopen=true,
    pdftitle={计算机应用论文},
    pdfauthor={作者姓名},
    pdfsubject={计算机应用},
    pdfkeywords={关键词}
}

% 列表环境设置
\setlist[enumerate]{
    itemsep=0pt,
    parsep=0pt,
    topsep=3pt,
    partopsep=0pt,
    leftmargin=2em
}

\setlist[itemize]{
    itemsep=0pt,
    parsep=0pt,
    topsep=3pt,
    partopsep=0pt,
    leftmargin=2em
}

% 数学公式设置
\allowdisplaybreaks[4]

% 定理环境（如果需要）
\newtheorem{theorem}{定理}[section]
\newtheorem{lemma}[theorem]{引理}
\newtheorem{proposition}[theorem]{命题}
\newtheorem{corollary}[theorem]{推论}
\newtheorem{definition}[theorem]{定义}
\newtheorem{example}[theorem]{例}
\newtheorem{remark}[theorem]{注}

% 证明环境
\renewcommand{\proofname}{证明}

% 算法环境（如果需要）
\RequirePackage{algorithm}
\RequirePackage{algorithmic}
\floatname{algorithm}{算法}
\renewcommand{\algorithmicrequire}{\textbf{输入:}}
\renewcommand{\algorithmicensure}{\textbf{输出:}}

% 中文日期格式
\renewcommand{\today}{\number\year 年 \number\month 月 \number\day 日}

% 文档开始时的设置
\AtBeginDocument{
    \thispagestyle{firstpage}
    \setlength{\baselineskip}{18pt}
}
