#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Markdown to Word converter for academic papers
将 Markdown 格式的学术论文转换为 Word 格式
"""

import re
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn


def setup_document_styles(doc):
    """设置文档样式"""
    # 设置正文样式
    normal_style = doc.styles['Normal']
    normal_font = normal_style.font
    normal_font.name = '宋体'
    normal_font.size = Pt(12)

    # 设置段落格式
    normal_paragraph = normal_style.paragraph_format
    normal_paragraph.first_line_indent = Pt(24)  # 首行缩进2字符
    normal_paragraph.line_spacing = 1.5  # 1.5倍行距

    # 创建标题样式
    try:
        heading1_style = doc.styles.add_style(
            'CustomHeading1', WD_STYLE_TYPE.PARAGRAPH)
        heading1_font = heading1_style.font
        heading1_font.name = '黑体'
        heading1_font.size = Pt(14)
        heading1_font.bold = True
        heading1_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
        heading1_style.paragraph_format.space_before = Pt(12)
        heading1_style.paragraph_format.space_after = Pt(6)
    except:
        pass

    try:
        heading2_style = doc.styles.add_style(
            'CustomHeading2', WD_STYLE_TYPE.PARAGRAPH)
        heading2_font = heading2_style.font
        heading2_font.name = '黑体'
        heading2_font.size = Pt(12)
        heading2_font.bold = True
        heading2_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
        heading2_style.paragraph_format.space_before = Pt(10)
        heading2_style.paragraph_format.space_after = Pt(5)
    except:
        pass

    try:
        heading3_style = doc.styles.add_style(
            'CustomHeading3', WD_STYLE_TYPE.PARAGRAPH)
        heading3_font = heading3_style.font
        heading3_font.name = '楷体'
        heading3_font.size = Pt(12)
        heading3_font.bold = False
        heading3_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
        heading3_style.paragraph_format.space_before = Pt(8)
        heading3_style.paragraph_format.space_after = Pt(4)
    except:
        pass


def parse_markdown_to_word(md_file, word_file):
    """将 Markdown 文件转换为 Word 文档"""

    # 创建新文档
    doc = Document()

    # 设置文档样式
    setup_document_styles(doc)

    # 读取 Markdown 文件
    with open(md_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # 按行处理
    lines = content.split('\n')
    i = 0

    while i < len(lines):
        line = lines[i].strip()

        if not line:
            i += 1
            continue

        # 处理标题
        if line.startswith('# '):
            # 一级标题
            title = line[2:].strip()
            p = doc.add_heading(title, level=1)
            try:
                p.style = doc.styles['CustomHeading1']
            except:
                pass

        elif line.startswith('## '):
            # 二级标题
            title = line[3:].strip()
            p = doc.add_heading(title, level=2)
            try:
                p.style = doc.styles['CustomHeading2']
            except:
                pass

        elif line.startswith('### '):
            # 三级标题
            title = line[4:].strip()
            p = doc.add_heading(title, level=3)
            try:
                p.style = doc.styles['CustomHeading3']
            except:
                pass

        # 处理数学公式块
        elif line.startswith('$$'):
            # 多行公式
            formula_lines = []
            if len(line) > 2:
                formula_lines.append(line[2:])
            i += 1
            while i < len(lines) and not lines[i].strip().endswith('$$'):
                formula_lines.append(lines[i])
                i += 1
            if i < len(lines):
                last_line = lines[i].strip()
                if last_line.endswith('$$'):
                    formula_lines.append(last_line[:-2])

            formula_text = '\n'.join(formula_lines).strip()
            p = doc.add_paragraph()
            p.alignment = WD_ALIGN_PARAGRAPH.CENTER
            # 改进公式显示
            run = p.add_run(f"{formula_text}")
            run.font.name = 'Times New Roman'
            run.font.size = Pt(11)
            run.italic = True

        # 处理普通段落
        else:
            # 检查是否包含行内公式
            if '$' in line and not line.startswith('$$'):
                p = doc.add_paragraph()
                # 处理行内公式
                parts = re.split(r'(\$[^$]+\$)', line)
                for part in parts:
                    if part.startswith('$') and part.endswith('$') and len(part) > 2:
                        # 这是公式
                        formula = part[1:-1]
                        run = p.add_run(formula)
                        run.font.name = 'Times New Roman'
                        run.font.size = Pt(11)
                        run.italic = True
                    else:
                        # 这是普通文本
                        if part.strip():
                            run = p.add_run(part)
                            run.font.name = '宋体'
                            run.font.size = Pt(12)
            else:
                # 普通段落
                if line:
                    p = doc.add_paragraph(line)
                    p.style = doc.styles['Normal']

        i += 1

    # 保存文档
    doc.save(word_file)
    print(f"转换完成！Word 文档已保存为: {word_file}")


if __name__ == "__main__":
    # 转换文件
    md_file = "paper_draft.md"
    word_file = "paper_draft.docx"

    try:
        parse_markdown_to_word(md_file, word_file)
        print("转换成功！")
    except Exception as e:
        print(f"转换失败: {e}")
