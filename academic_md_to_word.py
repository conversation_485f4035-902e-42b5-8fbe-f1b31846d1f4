#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
学术论文 Markdown 到 Word 转换器
专门针对中文学术论文格式优化
"""

import re
from docx import Document
from docx.shared import Inches, Pt, Cm
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn

def setup_academic_styles(doc):
    """设置学术论文样式"""
    
    # 设置页面边距
    sections = doc.sections
    for section in sections:
        section.top_margin = Cm(2.54)
        section.bottom_margin = Cm(2.54)
        section.left_margin = Cm(3.17)
        section.right_margin = Cm(3.17)
    
    # 设置正文样式
    normal_style = doc.styles['Normal']
    normal_font = normal_style.font
    normal_font.name = '宋体'
    normal_font.size = Pt(12)
    
    # 设置段落格式
    normal_paragraph = normal_style.paragraph_format
    normal_paragraph.first_line_indent = Pt(24)  # 首行缩进2字符
    normal_paragraph.line_spacing = 1.5  # 1.5倍行距
    normal_paragraph.space_after = Pt(6)
    
    # 创建论文标题样式
    try:
        title_style = doc.styles.add_style('PaperTitle', WD_STYLE_TYPE.PARAGRAPH)
        title_font = title_style.font
        title_font.name = '黑体'
        title_font.size = Pt(16)
        title_font.bold = True
        title_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
        title_style.paragraph_format.space_before = Pt(0)
        title_style.paragraph_format.space_after = Pt(18)
    except:
        pass
    
    # 创建一级标题样式
    try:
        heading1_style = doc.styles.add_style('CustomHeading1', WD_STYLE_TYPE.PARAGRAPH)
        heading1_font = heading1_style.font
        heading1_font.name = '黑体'
        heading1_font.size = Pt(14)
        heading1_font.bold = True
        heading1_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
        heading1_style.paragraph_format.space_before = Pt(18)
        heading1_style.paragraph_format.space_after = Pt(6)
        heading1_style.paragraph_format.first_line_indent = Pt(0)
    except:
        pass
    
    # 创建二级标题样式
    try:
        heading2_style = doc.styles.add_style('CustomHeading2', WD_STYLE_TYPE.PARAGRAPH)
        heading2_font = heading2_style.font
        heading2_font.name = '黑体'
        heading2_font.size = Pt(12)
        heading2_font.bold = True
        heading2_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
        heading2_style.paragraph_format.space_before = Pt(12)
        heading2_style.paragraph_format.space_after = Pt(6)
        heading2_style.paragraph_format.first_line_indent = Pt(0)
    except:
        pass
    
    # 创建三级标题样式
    try:
        heading3_style = doc.styles.add_style('CustomHeading3', WD_STYLE_TYPE.PARAGRAPH)
        heading3_font = heading3_style.font
        heading3_font.name = '楷体'
        heading3_font.size = Pt(12)
        heading3_font.bold = False
        heading3_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
        heading3_style.paragraph_format.space_before = Pt(6)
        heading3_style.paragraph_format.space_after = Pt(6)
        heading3_style.paragraph_format.first_line_indent = Pt(0)
    except:
        pass
    
    # 创建公式样式
    try:
        formula_style = doc.styles.add_style('Formula', WD_STYLE_TYPE.PARAGRAPH)
        formula_font = formula_style.font
        formula_font.name = 'Times New Roman'
        formula_font.size = Pt(11)
        formula_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
        formula_style.paragraph_format.space_before = Pt(6)
        formula_style.paragraph_format.space_after = Pt(6)
        formula_style.paragraph_format.first_line_indent = Pt(0)
    except:
        pass
    
    # 创建参考文献样式
    try:
        ref_style = doc.styles.add_style('Reference', WD_STYLE_TYPE.PARAGRAPH)
        ref_font = ref_style.font
        ref_font.name = '宋体'
        ref_font.size = Pt(10.5)
        ref_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
        ref_style.paragraph_format.space_after = Pt(3)
        ref_style.paragraph_format.first_line_indent = Pt(0)
        ref_style.paragraph_format.left_indent = Pt(21)  # 悬挂缩进
        ref_style.paragraph_format.hanging_indent = Pt(21)
    except:
        pass

def convert_academic_paper(md_file, word_file):
    """转换学术论文"""
    
    # 创建新文档
    doc = Document()
    
    # 设置学术论文样式
    setup_academic_styles(doc)
    
    # 读取 Markdown 文件
    with open(md_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 按行处理
    lines = content.split('\n')
    i = 0
    is_first_heading = True
    in_references = False
    
    while i < len(lines):
        line = lines[i].strip()
        
        if not line:
            i += 1
            continue
        
        # 检查是否进入参考文献部分
        if '参考文献' in line or 'References' in line:
            in_references = True
        
        # 处理标题
        if line.startswith('# '):
            # 论文主标题
            title = line[2:].strip()
            p = doc.add_paragraph(title)
            try:
                if is_first_heading:
                    p.style = doc.styles['PaperTitle']
                    is_first_heading = False
                else:
                    p.style = doc.styles['CustomHeading1']
            except:
                p.style = doc.styles['Heading 1']
            
        elif line.startswith('## '):
            # 二级标题
            title = line[3:].strip()
            p = doc.add_paragraph(title)
            try:
                p.style = doc.styles['CustomHeading2']
            except:
                p.style = doc.styles['Heading 2']
            
        elif line.startswith('### '):
            # 三级标题
            title = line[4:].strip()
            p = doc.add_paragraph(title)
            try:
                p.style = doc.styles['CustomHeading3']
            except:
                p.style = doc.styles['Heading 3']
        
        # 处理数学公式块
        elif line.startswith('$$'):
            # 多行公式
            formula_lines = []
            if len(line) > 2:
                formula_lines.append(line[2:])
            i += 1
            while i < len(lines) and not lines[i].strip().endswith('$$'):
                formula_lines.append(lines[i])
                i += 1
            if i < len(lines):
                last_line = lines[i].strip()
                if last_line.endswith('$$'):
                    formula_lines.append(last_line[:-2])
            
            formula_text = '\n'.join(formula_lines).strip()
            p = doc.add_paragraph(formula_text)
            try:
                p.style = doc.styles['Formula']
            except:
                p.alignment = WD_ALIGN_PARAGRAPH.CENTER
                for run in p.runs:
                    run.font.name = 'Times New Roman'
                    run.font.size = Pt(11)
        
        # 处理普通段落
        else:
            # 参考文献特殊处理
            if in_references and re.match(r'^\d+\.', line):
                p = doc.add_paragraph(line)
                try:
                    p.style = doc.styles['Reference']
                except:
                    p.style = doc.styles['Normal']
                    p.paragraph_format.first_line_indent = Pt(0)
                    p.paragraph_format.left_indent = Pt(21)
                    p.paragraph_format.hanging_indent = Pt(21)
            
            # 检查是否包含行内公式
            elif '$' in line and not line.startswith('$$'):
                p = doc.add_paragraph()
                # 处理行内公式
                parts = re.split(r'(\$[^$]+\$)', line)
                for part in parts:
                    if part.startswith('$') and part.endswith('$') and len(part) > 2:
                        # 这是公式
                        formula = part[1:-1]
                        run = p.add_run(formula)
                        run.font.name = 'Times New Roman'
                        run.font.size = Pt(11)
                        run.italic = True
                    else:
                        # 这是普通文本
                        if part.strip():
                            run = p.add_run(part)
                            run.font.name = '宋体'
                            run.font.size = Pt(12)
                
                # 设置段落样式
                if not in_references:
                    p.style = doc.styles['Normal']
            
            else:
                # 普通段落
                if line:
                    p = doc.add_paragraph(line)
                    if not in_references:
                        p.style = doc.styles['Normal']
                    else:
                        try:
                            p.style = doc.styles['Reference']
                        except:
                            p.style = doc.styles['Normal']
        
        i += 1
    
    # 保存文档
    doc.save(word_file)
    print(f"学术论文转换完成！Word 文档已保存为: {word_file}")

if __name__ == "__main__":
    # 转换文件
    md_file = "paper_draft.md"
    word_file = "paper_draft_academic.docx"
    
    try:
        convert_academic_paper(md_file, word_file)
        print("转换成功！")
    except Exception as e:
        print(f"转换失败: {e}")
        import traceback
        traceback.print_exc()
