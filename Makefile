# Makefile for JOCA LaTeX Template

MAIN = paper_joca_template
TEXFILES = $(MAIN).tex
PDFFILES = $(MAIN).pdf

# 默认目标
all: $(PDFFILES)

# 编译 PDF
$(MAIN).pdf: $(TEXFILES) joca.cls
	xelatex $(MAIN).tex
	xelatex $(MAIN).tex

# 清理临时文件
clean:
	rm -f *.aux *.log *.out *.toc *.bbl *.blg *.synctex.gz

# 清理所有生成文件
distclean: clean
	rm -f $(PDFFILES)

# 强制重新编译
rebuild: distclean all

.PHONY: all clean distclean rebuild
