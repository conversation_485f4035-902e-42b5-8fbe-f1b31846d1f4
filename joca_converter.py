#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
《计算机应用》期刊格式转换器
基于官方模板要求进行格式优化
"""

import re
from docx import Document
from docx.shared import Inches, Pt, Cm
from docx.enum.text import WD_ALIGN_PARAGRAPH, WD_LINE_SPACING
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.shared import OxmlElement, qn

# 导入数学公式清理函数
try:
    from improved_md_to_word import clean_latex_formula
except ImportError:
    # 如果导入失败，使用简化版本
    def clean_latex_formula(text):
        # 简化的公式处理
        text = text.replace('\\alpha', 'α').replace('\\beta', 'β').replace('\\gamma', 'γ')
        text = text.replace('\\delta', 'δ').replace('\\epsilon', 'ε').replace('\\sigma', 'σ')
        text = text.replace('\\rho', 'ρ').replace('\\pi', 'π').replace('\\sum', '∑')
        text = text.replace('\\mathbf{', '').replace('}', '').replace('\\text{', '')
        return text

def setup_joca_styles(doc):
    """设置《计算机应用》期刊样式"""
    
    # 设置页面边距（A4标准）
    sections = doc.sections
    for section in sections:
        section.top_margin = Cm(2.5)
        section.bottom_margin = Cm(2.5)
        section.left_margin = Cm(2.5)
        section.right_margin = Cm(2.5)
        section.page_height = Cm(29.7)  # A4
        section.page_width = Cm(21.0)   # A4
    
    # 设置正文样式
    normal_style = doc.styles['Normal']
    normal_font = normal_style.font
    normal_font.name = '宋体'
    normal_font.size = Pt(10.5)  # 《计算机应用》使用10.5号字体
    
    normal_paragraph = normal_style.paragraph_format
    normal_paragraph.first_line_indent = Pt(21)  # 首行缩进2字符
    normal_paragraph.line_spacing_rule = WD_LINE_SPACING.EXACTLY
    normal_paragraph.line_spacing = Pt(18)  # 固定行距18磅
    normal_paragraph.space_after = Pt(0)
    normal_paragraph.space_before = Pt(0)
    
    # 论文标题样式
    try:
        title_style = doc.styles.add_style('JOCATitle', WD_STYLE_TYPE.PARAGRAPH)
        title_font = title_style.font
        title_font.name = '黑体'
        title_font.size = Pt(14)
        title_font.bold = True
        title_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
        title_style.paragraph_format.space_before = Pt(0)
        title_style.paragraph_format.space_after = Pt(12)
        title_style.paragraph_format.line_spacing_rule = WD_LINE_SPACING.EXACTLY
        title_style.paragraph_format.line_spacing = Pt(18)
        title_style.paragraph_format.first_line_indent = Pt(0)
    except:
        pass
    
    # 摘要标题样式
    try:
        abstract_title_style = doc.styles.add_style('JOCAAbstractTitle', WD_STYLE_TYPE.PARAGRAPH)
        abstract_title_font = abstract_title_style.font
        abstract_title_font.name = '黑体'
        abstract_title_font.size = Pt(10.5)
        abstract_title_font.bold = True
        abstract_title_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
        abstract_title_style.paragraph_format.space_before = Pt(6)
        abstract_title_style.paragraph_format.space_after = Pt(3)
        abstract_title_style.paragraph_format.first_line_indent = Pt(0)
    except:
        pass
    
    # 摘要正文样式
    try:
        abstract_style = doc.styles.add_style('JOCAAbstractText', WD_STYLE_TYPE.PARAGRAPH)
        abstract_font = abstract_style.font
        abstract_font.name = '宋体'
        abstract_font.size = Pt(9)
        abstract_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.JUSTIFY
        abstract_style.paragraph_format.space_after = Pt(3)
        abstract_style.paragraph_format.first_line_indent = Pt(0)
        abstract_style.paragraph_format.line_spacing_rule = WD_LINE_SPACING.EXACTLY
        abstract_style.paragraph_format.line_spacing = Pt(16)
    except:
        pass
    
    # 一级标题样式
    try:
        heading1_style = doc.styles.add_style('JOCAHeading1', WD_STYLE_TYPE.PARAGRAPH)
        heading1_font = heading1_style.font
        heading1_font.name = '黑体'
        heading1_font.size = Pt(12)
        heading1_font.bold = True
        heading1_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
        heading1_style.paragraph_format.space_before = Pt(12)
        heading1_style.paragraph_format.space_after = Pt(6)
        heading1_style.paragraph_format.first_line_indent = Pt(0)
    except:
        pass
    
    # 二级标题样式
    try:
        heading2_style = doc.styles.add_style('JOCAHeading2', WD_STYLE_TYPE.PARAGRAPH)
        heading2_font = heading2_style.font
        heading2_font.name = '黑体'
        heading2_font.size = Pt(10.5)
        heading2_font.bold = True
        heading2_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
        heading2_style.paragraph_format.space_before = Pt(9)
        heading2_style.paragraph_format.space_after = Pt(6)
        heading2_style.paragraph_format.first_line_indent = Pt(0)
    except:
        pass
    
    # 三级标题样式
    try:
        heading3_style = doc.styles.add_style('JOCAHeading3', WD_STYLE_TYPE.PARAGRAPH)
        heading3_font = heading3_style.font
        heading3_font.name = '宋体'
        heading3_font.size = Pt(10.5)
        heading3_font.bold = True
        heading3_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
        heading3_style.paragraph_format.space_before = Pt(6)
        heading3_style.paragraph_format.space_after = Pt(3)
        heading3_style.paragraph_format.first_line_indent = Pt(0)
    except:
        pass
    
    # 公式样式
    try:
        formula_style = doc.styles.add_style('JOCAFormula', WD_STYLE_TYPE.PARAGRAPH)
        formula_font = formula_style.font
        formula_font.name = 'Times New Roman'
        formula_font.size = Pt(10.5)
        formula_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.CENTER
        formula_style.paragraph_format.space_before = Pt(6)
        formula_style.paragraph_format.space_after = Pt(6)
        formula_style.paragraph_format.first_line_indent = Pt(0)
    except:
        pass
    
    # 参考文献样式
    try:
        ref_style = doc.styles.add_style('JOCAReference', WD_STYLE_TYPE.PARAGRAPH)
        ref_font = ref_style.font
        ref_font.name = '宋体'
        ref_font.size = Pt(9)
        ref_style.paragraph_format.alignment = WD_ALIGN_PARAGRAPH.LEFT
        ref_style.paragraph_format.space_after = Pt(0)
        ref_style.paragraph_format.first_line_indent = Pt(0)
        ref_style.paragraph_format.left_indent = Pt(18)
        ref_style.paragraph_format.hanging_indent = Pt(18)
        ref_style.paragraph_format.line_spacing_rule = WD_LINE_SPACING.EXACTLY
        ref_style.paragraph_format.line_spacing = Pt(14)
    except:
        pass

def convert_joca_paper(md_file, word_file):
    """转换为《计算机应用》期刊格式"""
    
    # 创建新文档
    doc = Document()
    
    # 设置期刊样式
    setup_joca_styles(doc)
    
    # 读取 Markdown 文件
    with open(md_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 按行处理
    lines = content.split('\n')
    i = 0
    is_first_heading = True
    in_references = False
    in_abstract = False
    
    while i < len(lines):
        line = lines[i].strip()
        
        if not line:
            i += 1
            continue
        
        # 检查特殊部分
        if '参考文献' in line or 'References' in line:
            in_references = True
            in_abstract = False
        elif '摘要' in line or 'Abstract' in line:
            in_abstract = True
            in_references = False
        elif line.startswith('## ') and not ('摘要' in line or 'Abstract' in line):
            in_abstract = False
        
        # 处理标题
        if line.startswith('# '):
            title = line[2:].strip()
            p = doc.add_paragraph(title)
            try:
                if is_first_heading:
                    p.style = doc.styles['JOCATitle']
                    is_first_heading = False
                else:
                    p.style = doc.styles['JOCAHeading1']
            except:
                p.style = doc.styles['Heading 1']
            
        elif line.startswith('## '):
            title = line[3:].strip()
            p = doc.add_paragraph(title)
            try:
                if in_abstract or '摘要' in title or 'Abstract' in title:
                    p.style = doc.styles['JOCAAbstractTitle']
                else:
                    p.style = doc.styles['JOCAHeading2']
            except:
                p.style = doc.styles['Heading 2']
            
        elif line.startswith('### '):
            title = line[4:].strip()
            p = doc.add_paragraph(title)
            try:
                p.style = doc.styles['JOCAHeading3']
            except:
                p.style = doc.styles['Heading 3']
        
        # 处理数学公式块
        elif line.startswith('$$'):
            formula_lines = []
            first_line = line[2:].strip()
            if first_line:
                formula_lines.append(first_line)
            
            i += 1
            while i < len(lines):
                current_line = lines[i].strip()
                if current_line.endswith('$$'):
                    end_content = current_line[:-2].strip()
                    if end_content:
                        formula_lines.append(end_content)
                    break
                else:
                    formula_lines.append(current_line)
                i += 1
            
            formula_text = ' '.join(formula_lines).strip()
            cleaned_formula = clean_latex_formula(formula_text)
            
            p = doc.add_paragraph(cleaned_formula)
            try:
                p.style = doc.styles['JOCAFormula']
            except:
                p.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 处理普通段落
        else:
            # 参考文献特殊处理
            if in_references and re.match(r'^\d+\.', line):
                p = doc.add_paragraph(line)
                try:
                    p.style = doc.styles['JOCAReference']
                except:
                    p.style = doc.styles['Normal']
            
            # 行内公式处理
            elif '$' in line and not line.startswith('$$'):
                p = doc.add_paragraph()
                parts = re.split(r'(\$[^$]+\$)', line)
                for part in parts:
                    if part.startswith('$') and part.endswith('$') and len(part) > 2:
                        formula = part[1:-1]
                        cleaned_formula = clean_latex_formula(formula)
                        run = p.add_run(cleaned_formula)
                        run.font.name = 'Times New Roman'
                        run.font.size = Pt(10.5)
                    else:
                        if part.strip():
                            run = p.add_run(part)
                            run.font.name = '宋体'
                            run.font.size = Pt(10.5)
                
                # 设置段落样式
                if in_abstract:
                    try:
                        p.style = doc.styles['JOCAAbstractText']
                    except:
                        p.style = doc.styles['Normal']
                elif not in_references:
                    p.style = doc.styles['Normal']
            
            else:
                # 普通段落
                if line:
                    p = doc.add_paragraph(line)
                    if in_abstract:
                        try:
                            p.style = doc.styles['JOCAAbstractText']
                        except:
                            p.style = doc.styles['Normal']
                    elif in_references:
                        try:
                            p.style = doc.styles['JOCAReference']
                        except:
                            p.style = doc.styles['Normal']
                    else:
                        p.style = doc.styles['Normal']
        
        i += 1
    
    # 保存文档
    doc.save(word_file)
    print(f"《计算机应用》期刊格式转换完成！Word 文档已保存为: {word_file}")

if __name__ == "__main__":
    md_file = "paper_draft.md"
    word_file = "paper_draft_joca.docx"
    
    try:
        convert_joca_paper(md_file, word_file)
        print("转换成功！")
        print("\n《计算机应用》期刊格式特点：")
        print("1. 正文：宋体10.5号，首行缩进2字符，固定行距18磅")
        print("2. 标题：黑体，分级字号（14/12/10.5号）")
        print("3. 摘要：9号字体，无首行缩进")
        print("4. 参考文献：9号字体，悬挂缩进")
        print("5. 数学公式：Times New Roman 10.5号，居中")
        print("6. 页面边距：上下左右均为2.5cm")
    except Exception as e:
        print(f"转换失败: {e}")
        import traceback
        traceback.print_exc()
