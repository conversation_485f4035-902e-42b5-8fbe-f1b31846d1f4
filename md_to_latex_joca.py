#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Markdown 到《计算机应用》期刊 LaTeX 格式转换器
"""

import re
import os

def convert_md_to_latex_joca(md_file, tex_file):
    """将 Markdown 转换为《计算机应用》期刊 LaTeX 格式"""
    
    with open(md_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 开始构建 LaTeX 文档
    latex_content = []
    
    # 文档头部
    latex_content.append(r"\documentclass{joca}")
    latex_content.append("")
    latex_content.append(r"\begin{document}")
    latex_content.append("")
    
    # 按行处理
    lines = content.split('\n')
    i = 0
    in_abstract = False
    in_references = False
    first_heading = True
    
    while i < len(lines):
        line = lines[i].strip()
        
        if not line:
            latex_content.append("")
            i += 1
            continue
        
        # 检查特殊部分
        if '参考文献' in line or 'References' in line:
            in_references = True
            in_abstract = False
        elif '摘要' in line or 'Abstract' in line:
            in_abstract = True
            in_references = False
        elif line.startswith('## ') and not ('摘要' in line or 'Abstract' in line):
            in_abstract = False
        
        # 处理标题
        if line.startswith('# '):
            title = line[2:].strip()
            if first_heading:
                # 论文标题
                latex_content.append(f"\\papertitle{{{title}}}")
                latex_content.append("")
                first_heading = False
            else:
                # 一级标题
                latex_content.append(f"\\section{{{title}}}")
                latex_content.append("")
            
        elif line.startswith('## '):
            title = line[3:].strip()
            if '摘要' in title:
                latex_content.append("\\begin{paperabstract}")
                in_abstract = True
            elif 'Abstract' in title:
                latex_content.append("\\begin{englishabstract}")
                in_abstract = True
            elif '参考文献' in title or 'References' in title:
                latex_content.append("\\begin{thebibliography}{99}")
                latex_content.append("")
                in_references = True
            else:
                latex_content.append(f"\\subsection{{{title}}}")
                latex_content.append("")
            
        elif line.startswith('### '):
            title = line[4:].strip()
            latex_content.append(f"\\subsubsection{{{title}}}")
            latex_content.append("")
        
        # 处理数学公式块
        elif line.startswith('$$'):
            # 收集完整的公式块
            formula_lines = []
            first_line = line[2:].strip()
            if first_line:
                formula_lines.append(first_line)
            
            i += 1
            while i < len(lines):
                current_line = lines[i].strip()
                if current_line.endswith('$$'):
                    end_content = current_line[:-2].strip()
                    if end_content:
                        formula_lines.append(end_content)
                    break
                else:
                    formula_lines.append(current_line)
                i += 1
            
            formula_text = ' '.join(formula_lines).strip()
            latex_content.append("\\begin{equation}")
            latex_content.append(formula_text)
            latex_content.append("\\end{equation}")
            latex_content.append("")
        
        # 处理关键词
        elif line.startswith('**关键词') or line.startswith('关键词'):
            keywords = re.sub(r'\*\*关键词[：:]\*\*|关键词[：:]', '', line).strip()
            if in_abstract:
                latex_content.append("\\end{paperabstract}")
                latex_content.append("")
            latex_content.append(f"\\paperkeywords{{{keywords}}}")
            latex_content.append("")
            in_abstract = False
            
        elif line.startswith('**Keywords') or line.startswith('Keywords'):
            keywords = re.sub(r'\*\*Keywords[：:]\*\*|Keywords[：:]', '', line).strip()
            if in_abstract:
                latex_content.append("\\end{englishabstract}")
                latex_content.append("")
            latex_content.append(f"\\englishkeywords{{{keywords}}}")
            latex_content.append("")
            in_abstract = False
        
        # 处理参考文献
        elif in_references and re.match(r'^\d+\.', line):
            # 转换参考文献格式
            ref_text = re.sub(r'^\d+\.\s*', '', line)
            ref_num = re.match(r'^(\d+)\.', line).group(1)
            latex_content.append(f"\\bibitem{{ref{ref_num}}} {ref_text}")
            latex_content.append("")
        
        # 处理普通段落
        else:
            # 处理行内公式
            processed_line = process_inline_math(line)
            
            # 处理粗体和斜体
            processed_line = re.sub(r'\*\*(.*?)\*\*', r'\\textbf{\1}', processed_line)
            processed_line = re.sub(r'\*(.*?)\*', r'\\textit{\1}', processed_line)
            
            # 处理引用
            processed_line = re.sub(r'\[(\d+)\]', r'\\cite{ref\1}', processed_line)
            
            if processed_line:
                latex_content.append(processed_line)
                latex_content.append("")
        
        i += 1
    
    # 结束特殊环境
    if in_abstract:
        latex_content.append("\\end{paperabstract}")
        latex_content.append("")
    elif in_references:
        latex_content.append("\\end{thebibliography}")
        latex_content.append("")
    
    # 文档结尾
    latex_content.append("\\end{document}")
    
    # 写入文件
    with open(tex_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(latex_content))
    
    print(f"LaTeX 文档已保存为: {tex_file}")

def process_inline_math(text):
    """处理行内数学公式"""
    # 将 $...$ 保持不变，LaTeX 原生支持
    return text

def create_makefile():
    """创建编译用的 Makefile"""
    makefile_content = """# Makefile for JOCA LaTeX Template

MAIN = paper_joca_template
TEXFILES = $(MAIN).tex
PDFFILES = $(MAIN).pdf

# 默认目标
all: $(PDFFILES)

# 编译 PDF
$(MAIN).pdf: $(TEXFILES) joca.cls
	xelatex $(MAIN).tex
	xelatex $(MAIN).tex

# 清理临时文件
clean:
	rm -f *.aux *.log *.out *.toc *.bbl *.blg *.synctex.gz

# 清理所有生成文件
distclean: clean
	rm -f $(PDFFILES)

# 强制重新编译
rebuild: distclean all

.PHONY: all clean distclean rebuild
"""
    
    with open('Makefile', 'w', encoding='utf-8') as f:
        f.write(makefile_content)
    
    print("Makefile 已创建")

def create_compile_script():
    """创建编译脚本"""
    
    # Windows 批处理文件
    bat_content = """@echo off
echo 正在编译 LaTeX 文档...

xelatex paper_joca_template.tex
if %errorlevel% neq 0 (
    echo 第一次编译失败
    pause
    exit /b 1
)

xelatex paper_joca_template.tex
if %errorlevel% neq 0 (
    echo 第二次编译失败
    pause
    exit /b 1
)

echo 编译完成！
echo 生成的 PDF 文件：paper_joca_template.pdf
pause
"""
    
    with open('compile.bat', 'w', encoding='utf-8') as f:
        f.write(bat_content)
    
    # Linux/Mac shell 脚本
    sh_content = """#!/bin/bash
echo "正在编译 LaTeX 文档..."

xelatex paper_joca_template.tex
if [ $? -ne 0 ]; then
    echo "第一次编译失败"
    exit 1
fi

xelatex paper_joca_template.tex
if [ $? -ne 0 ]; then
    echo "第二次编译失败"
    exit 1
fi

echo "编译完成！"
echo "生成的 PDF 文件：paper_joca_template.pdf"
"""
    
    with open('compile.sh', 'w', encoding='utf-8') as f:
        f.write(sh_content)
    
    # 设置执行权限
    os.chmod('compile.sh', 0o755)
    
    print("编译脚本已创建：compile.bat (Windows) 和 compile.sh (Linux/Mac)")

if __name__ == "__main__":
    # 转换 Markdown 到 LaTeX
    md_file = "paper_draft.md"
    tex_file = "paper_draft_joca.tex"
    
    try:
        convert_md_to_latex_joca(md_file, tex_file)
        create_makefile()
        create_compile_script()
        
        print("\n转换完成！")
        print(f"生成的文件：")
        print(f"1. LaTeX 文档：{tex_file}")
        print(f"2. 类文件：joca.cls")
        print(f"3. 示例文档：paper_joca_template.tex")
        print(f"4. 编译脚本：compile.bat / compile.sh")
        print(f"5. Makefile：Makefile")
        
        print(f"\n编译方法：")
        print(f"Windows: 双击 compile.bat")
        print(f"Linux/Mac: ./compile.sh")
        print(f"手动编译: xelatex {tex_file}")
        
        print(f"\n注意事项：")
        print(f"1. 需要安装 XeLaTeX 编译器")
        print(f"2. 需要安装中文字体（SimSun, SimHei, FangSong）")
        print(f"3. 建议使用 TeX Live 2020 或更新版本")
        
    except Exception as e:
        print(f"转换失败: {e}")
        import traceback
        traceback.print_exc()
